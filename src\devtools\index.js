/*
 * @Author: Penk
 * @LastEditors: Penk
 * @LastEditTime: 2022-07-01 18:38:48
 * @FilePath: \vue-extension\src\devtools\index.js
 */

const fs = require("fs");
// import json from '../devtools.json'

// setTimeout(() => {
//   var ret = fs.readFileSync()('../devtools.json', {
//     encoding: 'utf8'
//   })

//   console.log(json);
// }, 5000);


window.chrome.devtools.panels.create('panel1', 'images/logo.png', `../panel1.html`, function (panel) {
  console.log('自定义面板创建成功！', panel); // 注意这个log一般看不到
});

// 创建自定义侧边栏
window.chrome.devtools.panels.elements.createSidebarPane("Images", function (sidebar) {
  // sidebar.setPage('../sidebar.html'); // 指定加载某个页面
  sidebar.setExpression('document.querySelectorAll("img")', 'All Images'); // 通过表达式来指定
  //sidebar.setObject({aaa: 111, bbb: 'Hello World!'}); // 直接设置显示某个对象
});


// 监听网络
function handleRequestFinished(request) {
  // 打印到前台
  console.log("Server IP: ", request.serverIPAddress);
}
window.chrome.devtools.network.onRequestFinished.addListener(handleRequestFinished);