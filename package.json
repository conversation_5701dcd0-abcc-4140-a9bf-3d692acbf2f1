{"name": "vue-extension", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "build-watch": "vue-cli-service build-watch"}, "dependencies": {"core-js": "^3.6.5", "element-ui": "^2.15.9", "jquery": "^3.6.0", "vue": "^2.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.9", "@vue/cli-plugin-eslint": "~4.5.9", "@vue/cli-service": "~4.5.9", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "copy-webpack-plugin": "^4.6.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-cli-plugin-chrome-ext": "0.0.5", "vue-template-compiler": "^2.6.11", "zip-webpack-plugin": "^4.0.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}