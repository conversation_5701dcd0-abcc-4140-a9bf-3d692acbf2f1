/*
 * @Author: Penk
 * @LastEditors: Penk
 * @LastEditTime: 2022-06-30 15:36:04
 * @FilePath: \vue-extension\src\content\index.js
 */
import Vue from "vue";
import AppComponent from "./App/App.vue";
// import $ from "jquery";

Vue.component("app-component", AppComponent);


// 客优云弄个固定窗口存放按钮
function initBox() {
  console.log("initBox...");

  var contentBox = document.createElement("div");
  contentBox.setAttribute("class", "penk");
  contentBox.setAttribute("id", "penk");
  document.body.appendChild(contentBox);
}

initBox();

setTimeout(() => {
  new Vue({
    el: "#penk",
    render: createElement => {
      return createElement(AppComponent);
    }
  });
}, 2000);